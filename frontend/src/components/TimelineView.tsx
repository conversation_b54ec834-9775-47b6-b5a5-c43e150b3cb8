import React from 'react';
import { DayData, CalendarState } from '../types';

interface TimelineViewProps {
  calendarData: CalendarState;
  darkMode: boolean;
  onEditTask: (date: Date, task: DayData) => void;
  onDeleteTask: (date: Date, taskId: string) => void;
}

const TimelineView: React.FC<TimelineViewProps> = ({ calendarData, darkMode, onEditTask, onDeleteTask }) => {
  // Group tasks by month
  const tasksByMonth: Record<string, { date: Date; task: DayData }[]> = {};
  
  Object.entries(calendarData.days).forEach(([, tasks]) => {
    tasks.forEach(task => {
      const date = new Date(task.date);
      const monthKey = `${date.getFullYear()}-${date.getMonth()}`;
      
      if (!tasksByMonth[monthKey]) {
        tasksByMonth[monthKey] = [];
      }
      
      tasksByMonth[monthKey].push({
        date,
        task
      });
    });
  });
  
  // Sort months
  const sortedMonths = Object.keys(tasksByMonth).sort((a, b) => {
    const [yearA, monthA] = a.split('-').map(Number);
    const [yearB, monthB] = b.split('-').map(Number);
    
    if (yearA !== yearB) {
      return yearA - yearB;
    }
    
    return monthA - monthB;
  });
  
  return (
    <div className={`w-full rounded-xl border shadow-lg transition-colors duration-200 ${darkMode ? 'border-gray-700 shadow-gray-900/20 bg-gray-800' : 'border-gray-300 shadow-gray-200 bg-white'}`}>
      <div className="p-4">
        <h2 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Tidslinje</h2>
        
        {sortedMonths.length === 0 ? (
          <div className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            Ingen opgaver fundet
          </div>
        ) : (
          <div className="space-y-6">
            {sortedMonths.map(monthKey => {
              const [year, month] = monthKey.split('-').map(Number);
              const monthName = new Date(year, month, 1).toLocaleDateString('da-DK', { month: 'long' });
              const tasks = tasksByMonth[monthKey];
              
              // Sort tasks by date
              tasks.sort((a, b) => a.date.getTime() - b.date.getTime());
              
              return (
                <div key={monthKey} className="space-y-2">
                  <h3 className={`font-bold text-lg ${darkMode ? 'text-gray-200' : 'text-gray-700'}`}>
                    {monthName} {year}
                  </h3>
                  
                  <div className="relative pl-8 space-y-4">
                    {/* Timeline line */}
                    <div className={`absolute left-3 top-0 bottom-0 w-0.5 ${darkMode ? 'bg-gray-600' : 'bg-gray-300'}`} />
                    
                    {tasks.map(({ date, task }) => (
                      <div key={task.id} className="relative">
                        {/* Timeline dot */}
                        <div 
                          className="absolute left-[-20px] top-1.5 w-3 h-3 rounded-full border-2"
                          style={{ 
                            backgroundColor: task.color || (darkMode ? '#4f46e5' : '#4f46e5'),
                            borderColor: darkMode ? '#1f2937' : 'white'
                          }}
                        />
                        
                        <div className={`p-3 rounded-lg border transition-all hover:shadow-md ${darkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-200 hover:bg-gray-50'}`}>
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="flex items-center space-x-2">
                                <h4 className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>{task.name}</h4>
                                <div className={`text-xs px-2 py-0.5 rounded ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-700'}`}>
                                  {date.toLocaleDateString('da-DK', { day: 'numeric', month: 'short' })}
                                </div>
                              </div>
                              
                              {task.description && (
                                <p className={`text-sm mt-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{task.description}</p>
                              )}
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => onEditTask(date, task)}
                                className={`p-1 rounded-full hover:bg-opacity-10 ${darkMode ? 'hover:bg-gray-300' : 'hover:bg-gray-700'}`}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                </svg>
                              </button>
                              
                              <button
                                onClick={() => {
                                  if (window.confirm('Er du sikker på, at du vil slette denne opgave?')) {
                                    onDeleteTask(date, task.id);
                                  }
                                }}
                                className={`p-1 rounded-full hover:bg-opacity-10 ${darkMode ? 'hover:bg-red-300' : 'hover:bg-red-100'}`}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${darkMode ? 'text-red-400' : 'text-red-500'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default TimelineView;
