import { useState, useEffect } from 'react';
import Day from './Day';
import { DayData, CalendarState, ViewType } from '../types';
import { useAuth } from '../contexts/AuthContext';

interface CalendarProps {
  darkMode: boolean;
  planName: string;
  onShowAuth?: () => void;
  onHideAuth?: () => void;
}

const Calendar: React.FC<CalendarProps> = ({ darkMode, planName, onShowAuth, onHideAuth }) => {
  const { user, signout } = useAuth();
  const [showWeekends, setShowWeekends] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  // We'll keep the viewType state for future use
  const [viewType] = useState<ViewType>('calendar');

  // Print functions
  const printCalendarView = () => {
    // Create a new window for printing with a specific name to allow navigation
    const printWindow = window.open('', 'printWindow', 'width=1000,height=800,toolbar=yes,scrollbars=yes');
    if (!printWindow) return;

    // Focus on the new window
    printWindow.focus();

    // Clear any existing content
    printWindow.document.body.innerHTML = '';

    // Set the filename for download (using the plan name)
    const safeFileName = planName.replace(/[^a-z0-9]/gi, '_');

    // Start building HTML content
    let content = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${safeFileName}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }

          h2 { text-align: center; margin: 30px 0 15px; font-size: 1.5em; }
          .calendar { width: 100%; border-collapse: collapse; margin-bottom: 30px; table-layout: fixed; }
          .calendar th { padding: 10px; background-color: #f0f0f0; border: 1px solid #ddd; }
          .calendar td { min-height: 100px; vertical-align: top; border: 1px solid #ddd; padding: 8px; width: 14%; }
          .date { font-weight: bold; margin-bottom: 8px; }
          .other-month { background-color: #f9f9f9; opacity: 0.6; }
          .empty-cell { background-color: #f9f9f9; }
          .task { margin-bottom: 8px; padding: 6px; border-radius: 4px; }
          .task small { display: block; margin-top: 4px; font-size: 0.85em; line-height: 1.3; white-space: pre-line; }
          .month-divider { page-break-before: always; }
          .week-number-header { width: 30px; text-align: center; background-color: #e0e0e0; }
          .week-number { width: 30px; text-align: center; background-color: #f5f5f5; vertical-align: middle !important; }
          .week-number-circle { display: flex; align-items: center; justify-content: center; width: 26px; height: 26px; background-color: #4f46e5; color: white; border-radius: 50%; margin: 0 auto; font-weight: bold; }
          .header-container { display: flex; justify-content: flex-end; align-items: center; margin-bottom: 20px; }
          .gem-button {
            display: inline-flex;
            align-items: center;
            background-color: #10b981;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            border: none;
            font-size: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
          .gem-button svg {
            margin-right: 8px;
            width: 20px;
            height: 20px;
          }
          .info-button-container:hover .info-tooltip {
            opacity: 1;
            visibility: visible;
          }
          @media print {
            body { padding: 0; -webkit-print-color-adjust: exact; print-color-adjust: exact; color-adjust: exact; }
            .no-print { display: none; }
            .print-instructions { display: none; }
            .month-divider:first-of-type { page-break-before: avoid; }
            .calendar { page-break-inside: avoid; }
            .calendar td {
              min-height: 80px;
              height: auto;
              page-break-inside: avoid;
            }
            .task {
              break-inside: avoid;
              page-break-inside: avoid;
              margin-bottom: 6px;
              padding: 6px;
              border-radius: 4px;
            }
            h2 { margin-top: 0; }
            .gem-button { display: none; }
            .info-button-container { display: none; }
            .header-container { display: none; }
          }
        </style>
        <script>
          // Set the filename for download
          document.title = "${safeFileName}";

          // Function to save the page as PDF
          function savePDF() {
            // Use setTimeout to ensure the browser has time to render the page
            setTimeout(function() {
              try {
                window.print();
              } catch (e) {
                console.error('Print error:', e);
                alert('Der opstod en fejl ved udskrivning. Prøv venligst igen.');
              }
            }, 200);
          }

          // We don't auto-print anymore, user will click the Print button when ready
          // This allows users to view the content before printing
          window.addEventListener('load', function() {
            console.log('Print window loaded and ready');
          });
        </script>
      </head>
      <body>
        <div class="header-container">
          <div style="display: flex; align-items: center; gap: 10px;">
            <button class="gem-button" onclick="savePDF()">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd" />
              </svg>
              Print
            </button>
            <div class="info-button-container" style="position: relative; display: inline-block;">
              <div class="info-button" style="width: 20px; height: 20px; border-radius: 50%; background-color: #3b82f6; color: white; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; cursor: pointer;"
                   onmouseover="this.nextElementSibling.style.opacity='1'; this.nextElementSibling.style.visibility='visible';"
                   onmouseout="this.nextElementSibling.style.opacity='0'; this.nextElementSibling.style.visibility='hidden';">
                i
              </div>
              <div class="info-tooltip" style="position: absolute; top: 25px; right: 0; background-color: #1f2937; color: white; padding: 12px; border-radius: 6px; font-size: 12px; opacity: 0; visibility: hidden; transition: opacity 0.3s, visibility 0.3s; z-index: 1000; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); width: 20vw; white-space: normal;">
                <strong>For at fjerne sidehoveder og sidefødder:</strong><br>
                1. Klik på "Print" knappen<br>
                2. I print dialogen, klik på "Flere indstillinger"<br>
                3. Fjern fluebenet ved "Sidehoveder og sidefødder"<br>
                4. Klik "Print" eller "Gem som PDF"
                <div style="position: absolute; bottom: 100%; right: 10px; width: 0; height: 0; border-left: 5px solid transparent; border-right: 5px solid transparent; border-bottom: 5px solid #1f2937;"></div>
              </div>
            </div>
          </div>
        </div>
    `;

    // Find all months with tasks
    const activeMonths: { year: number; month: number }[] = [];

    // Add current month
    activeMonths.push({ year: currentMonth.getFullYear(), month: currentMonth.getMonth() });

    // Find all months with tasks
    Object.keys(calendarData.days).forEach(dateKey => {
      const date = new Date(dateKey);
      const year = date.getFullYear();
      const month = date.getMonth();

      // Check if this month is already in the list
      const exists = activeMonths.some(m => m.year === year && m.month === month);
      if (!exists) {
        activeMonths.push({ year, month });
      }
    });

    // Sort months chronologically
    activeMonths.sort((a, b) => {
      if (a.year !== b.year) {
        return a.year - b.year;
      }
      return a.month - b.month;
    });

    // Generate calendar for each active month
    activeMonths.forEach((activeMonth, index) => {
      const { year, month } = activeMonth;
      const monthName = new Date(year, month, 1).toLocaleDateString('da-DK', { month: 'long' });

      // Add month header
      content += `
        <div class="${index > 0 ? 'month-divider' : ''}">
          <h2>${monthName} ${year}</h2>
          <table class="calendar">
            <tr>
              <th class="week-number-header">Uge</th>
              <th>Mandag</th>
              <th>Tirsdag</th>
              <th>Onsdag</th>
              <th>Torsdag</th>
              <th>Fredag</th>
              ${showWeekends ? '<th>Lørdag</th><th>Søndag</th>' : ''}
            </tr>
      `;

      // Generate weeks for this month
      const weeks = generateCalendarData(year, month, true); // Added parameter to filter out days from other months
      weeks.forEach(week => {
        // Get the week number from the first day of the week
        const weekNumber = getWeekNumber(week[0].date);

        content += '<tr>';

        // Add week number cell
        content += `
          <td class="week-number">
            <div class="week-number-circle">${weekNumber}</div>
          </td>
        `;

        // Calculate the day of week for the first day in this week
        const firstDayOfWeek = week[0].date.getDay() || 7; // Convert Sunday (0) to 7 for easier calculation

        // Add empty cells for days before the first day of the month
        if (week[0].date.getDate() === 1) {
          for (let i = 1; i < firstDayOfWeek; i++) {
            content += '<td class="empty-cell"></td>';
          }
        }

        // Add the actual days
        week.forEach(day => {
          const dateStr = day.date.getDate();
          const tasks = day.dayData || [];

          content += `
            <td>
              <div class="date">${dateStr}</div>
          `;

          // Show all tasks in the printed version
          tasks.forEach(task => {
            content += `
              <div class="task" style="background-color: ${task.color || '#f0f0f0'}; color: ${task.color ? 'white' : 'black'};">
                <strong>${task.name}</strong>
                ${task.description ? `<small>${task.description.replace(/\n/g, '<br>')}</small>` : ''}
              </div>
            `;
          });

          content += '</td>';
        });

        // Add empty cells for days after the last day of the month
        const lastDayOfWeek = week[week.length - 1].date.getDay() || 7;
        const daysToAdd = showWeekends ? (7 - lastDayOfWeek) : (5 - lastDayOfWeek);

        if (week[week.length - 1].date.getDate() === new Date(year, month + 1, 0).getDate()) {
          for (let i = 0; i < daysToAdd; i++) {
            content += '<td class="empty-cell"></td>';
          }
        }
        content += '</tr>';
      });

      content += '</table></div>';
    });

    // Close HTML
    content += `
      </body>
      </html>
    `;

    // Write to the new window without triggering print automatically
    printWindow.document.open();
    printWindow.document.write(content);
    printWindow.document.close();

    // The window's load event handler will just log that it's ready
    // The user will need to click the Print button to print
    printWindow.onload = () => {
      // Just log that the window is loaded, no automatic printing
      console.log('Calendar print window loaded');
    };
  };

  const printTimelineView = () => {
    // Create a new window for printing with a specific name to allow navigation
    const printWindow = window.open('', 'printWindow', 'width=1000,height=800,toolbar=yes,scrollbars=yes');
    if (!printWindow) return;

    // Focus on the new window
    printWindow.focus();

    // Clear any existing content
    printWindow.document.body.innerHTML = '';

    // No timestamp needed

    // Set the filename for download (using the plan name)
    const safeFileName = planName.replace(/[^a-z0-9]/gi, '_');

    // Start building HTML content
    let content = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${safeFileName}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }

          .timeline { width: 100%; }
          .month { margin-bottom: 30px; }
          .month-name { font-size: 18px; font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
          .task { margin-bottom: 15px; padding: 10px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
          .task-date { font-weight: bold; margin-bottom: 5px; }
          .task-name { font-size: 16px; margin-bottom: 5px; }
          .task-description { font-size: 14px; color: #666; white-space: pre-line; }
          .header-container { display: flex; justify-content: flex-end; align-items: center; margin-bottom: 20px; }
          .gem-button {
            display: inline-flex;
            align-items: center;
            background-color: #10b981;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            border: none;
            font-size: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
          .gem-button svg {
            margin-right: 8px;
            width: 20px;
            height: 20px;
          }
          .info-button-container:hover .info-tooltip {
            opacity: 1;
            visibility: visible;
          }
          @media print {
            body { padding: 0; -webkit-print-color-adjust: exact; print-color-adjust: exact; color-adjust: exact; }
            .no-print { display: none; }
            .print-instructions { display: none; }
            .gem-button { display: none; }
            .info-button-container { display: none; }
            .header-container { display: none; }
          }
        </style>
        <script>
          // Set the filename for download
          document.title = "${safeFileName}";

          // Function to save the page as PDF
          function savePDF() {
            // Use setTimeout to ensure the browser has time to render the page
            setTimeout(function() {
              try {
                window.print();
              } catch (e) {
                console.error('Print error:', e);
                alert('Der opstod en fejl ved udskrivning. Prøv venligst igen.');
              }
            }, 200);
          }

          // We don't auto-print anymore, user will click the Print button when ready
          // This allows users to view the content before printing
          window.addEventListener('load', function() {
            console.log('Print window loaded and ready');
          });
        </script>
      </head>
      <body>
        <div class="header-container">
          <div style="display: flex; align-items: center; gap: 10px;">
            <button class="gem-button" onclick="savePDF()">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd" />
              </svg>
              Print
            </button>
            <div class="info-button-container" style="position: relative; display: inline-block;">
              <div class="info-button" style="width: 20px; height: 20px; border-radius: 50%; background-color: #3b82f6; color: white; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; cursor: pointer;"
                   onmouseover="this.nextElementSibling.style.opacity='1'; this.nextElementSibling.style.visibility='visible';"
                   onmouseout="this.nextElementSibling.style.opacity='0'; this.nextElementSibling.style.visibility='hidden';">
                i
              </div>
              <div class="info-tooltip" style="position: absolute; top: 25px; right: 0; background-color: #1f2937; color: white; padding: 12px; border-radius: 6px; font-size: 12px; opacity: 0; visibility: hidden; transition: opacity 0.3s, visibility 0.3s; z-index: 1000; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); width: 20vw; white-space: normal;">
                <strong>For at fjerne sidehoveder og sidefødder:</strong><br>
                1. Klik på "Print" knappen<br>
                2. I print dialogen, klik på "Flere indstillinger"<br>
                3. Fjern fluebenet ved "Sidehoveder og sidefødder"<br>
                4. Klik "Print" eller "Gem som PDF"
                <div style="position: absolute; bottom: 100%; right: 10px; width: 0; height: 0; border-left: 5px solid transparent; border-right: 5px solid transparent; border-bottom: 5px solid #1f2937;"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="timeline">
    `;

    // Group tasks by month
    const tasksByMonth: Record<string, { date: Date; task: DayData }[]> = {};

    Object.entries(calendarData.days).forEach(([, tasks]) => {
      tasks.forEach(task => {
        const date = new Date(task.date);
        const monthKey = `${date.getFullYear()}-${date.getMonth()}`;

        if (!tasksByMonth[monthKey]) {
          tasksByMonth[monthKey] = [];
        }

        tasksByMonth[monthKey].push({
          date,
          task
        });
      });
    });

    // Sort months
    const sortedMonths = Object.keys(tasksByMonth).sort((a, b) => {
      const [yearA, monthA] = a.split('-').map(Number);
      const [yearB, monthB] = b.split('-').map(Number);

      if (yearA !== yearB) {
        return yearA - yearB;
      }

      return monthA - monthB;
    });

    // Generate timeline HTML
    sortedMonths.forEach(monthKey => {
      const [year, month] = monthKey.split('-').map(Number);
      const monthName = new Date(year, month, 1).toLocaleDateString('da-DK', { month: 'long' });
      const tasks = tasksByMonth[monthKey];

      // Sort tasks by date
      tasks.sort((a, b) => a.date.getTime() - b.date.getTime());

      content += `
        <div class="month">
          <div class="month-name">${monthName} ${year}</div>
      `;

      tasks.forEach(({ date, task }) => {
        content += `
          <div class="task" style="border-left: 4px solid ${task.color || '#4f46e5'}; background-color: ${task.color ? task.color + '10' : '#f9f9f9'};">
            <div class="task-date">${date.toLocaleDateString('da-DK', { day: 'numeric', month: 'short', year: 'numeric' })}</div>
            <div class="task-name">${task.name}</div>
            ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
          </div>
        `;
      });

      content += '</div>';
    });

    // Close HTML
    content += `
        </div>
      </body>
      </html>
    `;

    // Write to the new window without triggering print automatically
    printWindow.document.open();
    printWindow.document.write(content);
    printWindow.document.close();

    // The window's load event handler will just log that it's ready
    // The user will need to click the Print button to print
    printWindow.onload = () => {
      // Just log that the window is loaded, no automatic printing
      console.log('Timeline print window loaded');
    };
  };

  // Helper function to generate calendar data for printing
  const generateCalendarData = (yearParam?: number, monthParam?: number, currentMonthOnly: boolean = false) => {
    const year = yearParam !== undefined ? yearParam : currentMonth.getFullYear();
    const month = monthParam !== undefined ? monthParam : currentMonth.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // Organize days into weeks (7 days per row)
    const weeks = [];
    let weekDays = [];

    // Find the first Monday of the month or before (start of week)
    const firstDayOfWeek = new Date(year, month, 1);
    while (firstDayOfWeek.getDay() !== 1) { // 1 is Monday
      firstDayOfWeek.setDate(firstDayOfWeek.getDate() - 1);
    }

    // Calculate the last day we need to display
    const lastDayOfMonth = new Date(year, month, daysInMonth);
    const lastDayOfCalendar = new Date(lastDayOfMonth);
    while (lastDayOfCalendar.getDay() !== 0) { // 0 is Sunday
      lastDayOfCalendar.setDate(lastDayOfCalendar.getDate() + 1);
    }

    // Generate all dates from first Monday to last Sunday
    const currentDate = new Date(firstDayOfWeek);
    while (currentDate <= lastDayOfCalendar) {
      const dateKey = currentDate.toISOString().split('T')[0];
      const isCurrentMonth = currentDate.getMonth() === month;

      // Skip days not in current month if currentMonthOnly is true
      if (currentMonthOnly && !isCurrentMonth) {
        currentDate.setDate(currentDate.getDate() + 1);
        continue;
      }

      // Always fetch tasks for the date, regardless of whether it's in the current month
      const dayData = calendarData.days[dateKey] || [];

      // Check if we should include this day
      const isWeekend = currentDate.getDay() === 0 || currentDate.getDay() === 6;

      // Only add the day if it's not a weekend or if showWeekends is true
      if (!isWeekend || showWeekends) {
        weekDays.push({
          date: new Date(currentDate),
          dateKey,
          dayData,
          isCurrentMonth,
          isWeekend
        });
      }

      // If we have 7 days (with weekends) or 5 days (without weekends) or it's Sunday, start a new week
      const daysPerWeek = showWeekends ? 7 : 5;
      if (weekDays.length === daysPerWeek || currentDate.getDay() === 0) {
        if (weekDays.length > 0) {
          // Only add the week if it has at least one day in the current month
          const hasCurrentMonthDay = weekDays.some(day => day.isCurrentMonth);
          if (hasCurrentMonthDay) {
            weeks.push([...weekDays]);
          }
          weekDays = [];
        }
      }

      // Move to the next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Add any remaining days as the last week
    if (weekDays.length > 0) {
      weeks.push(weekDays);
    }

    return weeks;
  };
  const [calendarData, setCalendarData] = useState<CalendarState>(() => {
    const savedData = localStorage.getItem('calendarData');
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);

        // Convert string dates back to Date objects
        const days: Record<string, DayData[]> = {};

        // Handle both old format (single task per day) and new format (array of tasks)
        Object.entries(parsed.days).forEach(([key, value]) => {
          if (Array.isArray(value)) {
            // New format - array of tasks
            days[key] = (value as DayData[]).map(day => ({
              ...day,
              date: new Date(day.date),
              endDate: day.endDate ? new Date(day.endDate) : undefined
            }));
          } else {
            // Old format - single task
            const day = value as DayData;
            days[key] = [{
              ...day,
              date: new Date(day.date),
              endDate: day.endDate ? new Date(day.endDate) : undefined
            }];
          }
        });

        return { days };
      } catch (e) {
        console.error('Failed to parse saved data:', e);
      }
    }

    // Default empty state
    return { days: {} };
  });

  useEffect(() => {
    localStorage.setItem('calendarData', JSON.stringify(calendarData));
  }, [calendarData]);

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Helper function to get the first day of the month (not used in current implementation)
  // const getFirstDayOfMonth = (year: number, month: number) => {
  //   return new Date(year, month, 1).getDay();
  // };

  const handlePrevMonth = () => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(newMonth.getMonth() - 1);
      return newMonth;
    });
  };

  const handleNextMonth = () => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(newMonth.getMonth() + 1);
      return newMonth;
    });
  };

  const handleSaveDay = (dayData: DayData) => {
    const dateKey = dayData.date.toISOString().split('T')[0];

    // Create a copy of the days object
    const updatedDays = { ...calendarData.days };

    // Add the new day data to the array for this date
    if (!updatedDays[dateKey]) {
      updatedDays[dateKey] = [];
    }

    // If editing an existing task, update it
    const existingIndex = updatedDays[dateKey].findIndex(day => day.id === dayData.id);
    if (existingIndex >= 0) {
      updatedDays[dateKey][existingIndex] = dayData;
    } else {
      // Otherwise add as a new task
      updatedDays[dateKey].push(dayData);
    }

    // If the day has an end date, create entries for all days until the end date
    if (dayData.endDate) {
      const startDate = new Date(dayData.date);
      const endDate = new Date(dayData.endDate);

      // Start from the day after the start date
      const currentDate = new Date(startDate);
      currentDate.setDate(currentDate.getDate() + 1);

      // Loop through all days from start+1 to end date
      while (currentDate <= endDate) {
        const dateKey = currentDate.toISOString().split('T')[0];

        // Skip weekends if not showing weekends
        const isWeekend = currentDate.getDay() === 0 || currentDate.getDay() === 6;
        if (!(isWeekend && !showWeekends)) {
          // Create a new day data with the same properties but different date
          const newDayData = {
            ...dayData,
            id: crypto.randomUUID(),
            date: new Date(currentDate)
          };

          // Initialize the array if needed
          if (!updatedDays[dateKey]) {
            updatedDays[dateKey] = [];
          }

          // Add the new day data
          updatedDays[dateKey].push(newDayData);
        }

        // Move to the next day
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }

    setCalendarData(prev => ({
      ...prev,
      days: updatedDays
    }));
  };

  const handleDeleteDay = (date: Date, taskId: string) => {
    const dateKey = date.toISOString().split('T')[0];

    // Create a copy of the days object
    const updatedDays = { ...calendarData.days };

    // Find the task to delete
    if (!updatedDays[dateKey] || updatedDays[dateKey].length === 0) {
      return; // No tasks for this date
    }

    const taskIndex = updatedDays[dateKey].findIndex(day => day.id === taskId);
    if (taskIndex === -1) {
      return; // Task not found
    }

    const dayData = updatedDays[dateKey][taskIndex];

    // Check if this is a start date or a continuation date
    const isStartDate = dayData && dayData.date.toISOString().split('T')[0] === dateKey;

    if (isStartDate && dayData?.endDate) {
      // If it's a start date with an end date, delete all related days
      const startDate = new Date(date);
      const endDate = new Date(dayData.endDate);

      // Remove this task from the start date
      updatedDays[dateKey].splice(taskIndex, 1);
      if (updatedDays[dateKey].length === 0) {
        delete updatedDays[dateKey];
      }

      // Start from the start date itself to include the chosen day
      const currentDate = new Date(startDate);

      // Loop through all days from start to end date
      while (currentDate <= endDate) {
        const dateKey = currentDate.toISOString().split('T')[0];

        // Find and remove related tasks
        if (updatedDays[dateKey]) {
          // Find tasks that are related to the deleted task
          const relatedTasks = updatedDays[dateKey].filter(day => {
            // Check if this task is related to the original task
            return day.name === dayData.name &&
                   day.color === dayData.color &&
                   day.description === dayData.description;
          });

          // Remove the related tasks
          relatedTasks.forEach(task => {
            const index = updatedDays[dateKey].findIndex(d => d.id === task.id);
            if (index !== -1) {
              updatedDays[dateKey].splice(index, 1);
            }
          });

          // Remove the date entry if no tasks remain
          if (updatedDays[dateKey].length === 0) {
            delete updatedDays[dateKey];
          }
        }

        // Move to the next day
        currentDate.setDate(currentDate.getDate() + 1);
      }
    } else {
      // If it's not a start date or doesn't have an end date, just delete this task
      updatedDays[dateKey].splice(taskIndex, 1);
      if (updatedDays[dateKey].length === 0) {
        delete updatedDays[dateKey];
      }
    }

    setCalendarData(prev => ({
      ...prev,
      days: updatedDays
    }));
  };

  // Helper function to get ISO week number
  const getWeekNumber = (date: Date): number => {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay() || 7));
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
  };

  const renderCalendar = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();

    const daysInMonth = getDaysInMonth(year, month);

    // Create an array of all days in the month
    const days = [];

    // Add days from the current month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dateKey = date.toISOString().split('T')[0];
      const dayData = calendarData.days[dateKey];

      days.push({
        date,
        dateKey,
        dayData,
        isCurrentMonth: true
      });
    }

    // Organize days into weeks (7 days per row)
    const weeks = [];
    let weekDays = [];

    // We'll display Monday (1) through Sunday (0)

    // Find the first Monday of the month or before (start of week)
    const firstDayOfWeek = new Date(year, month, 1);
    while (firstDayOfWeek.getDay() !== 1) { // 1 is Monday
      firstDayOfWeek.setDate(firstDayOfWeek.getDate() - 1);
    }

    // Calculate the last day we need to display
    const lastDayOfMonth = new Date(year, month, daysInMonth);
    const lastDayOfCalendar = new Date(lastDayOfMonth);
    while (lastDayOfCalendar.getDay() !== 0) { // 0 is Sunday
      lastDayOfCalendar.setDate(lastDayOfCalendar.getDate() + 1);
    }

    // Generate all dates from first Sunday to last Saturday
    const currentDate = new Date(firstDayOfWeek);
    while (currentDate <= lastDayOfCalendar) {
      const dateKey = currentDate.toISOString().split('T')[0];
      const isCurrentMonth = currentDate.getMonth() === month;
      // Always fetch tasks for the date, regardless of whether it's in the current month
      const dayData = calendarData.days[dateKey];

      // Check if we should include this day
      const isWeekend = currentDate.getDay() === 0 || currentDate.getDay() === 6;

      // Only add the day if it's not a weekend or if showWeekends is true
      if (!isWeekend || showWeekends) {
        weekDays.push({
          date: new Date(currentDate),
          dateKey,
          dayData,
          isCurrentMonth,
          isWeekend
        });
      }

      // If we have 7 days (with weekends) or 5 days (without weekends) or it's Sunday, start a new week
      const daysPerWeek = showWeekends ? 7 : 5;
      if (weekDays.length === daysPerWeek || currentDate.getDay() === 0) {
        if (weekDays.length > 0) {
          // Only add the week if it has at least one day in the current month
          const hasCurrentMonthDay = weekDays.some(day => day.isCurrentMonth);
          if (hasCurrentMonthDay) {
            weeks.push([...weekDays]);
          }
          weekDays = [];
        }
      }

      // Move to the next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Render the weeks
    return weeks.map((week, weekIndex) => {
      // Get the week number from the first day of the week
      const weekNumber = getWeekNumber(week[0].date);

      return (
        <div key={`week-${weekIndex}`} className="flex flex-row justify-center gap-2 p-3 rounded-xl relative">
          {/* Week number */}
          <div
            className={`absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-10 flex items-center justify-center w-8 h-8 rounded-full ${
              darkMode ? 'bg-gray-700 text-gray-200' : 'bg-gray-200 text-gray-700'
            } font-bold text-sm`}
          >
            {weekNumber}
          </div>

          {/* Week container with background */}
          <div className={`flex flex-row justify-center gap-2 p-3 rounded-xl w-full ${darkMode ? 'bg-gray-800/20' : 'bg-gray-100/70'}`}>
            {week.map(day => (
              <div key={day.dateKey} className="flex-1 min-w-0">
                <Day
                  date={day.date}
                  dayData={day.dayData}
                  onSave={handleSaveDay}
                  onDelete={handleDeleteDay}
                  darkMode={darkMode}
                  isWeekend={day.isWeekend}
                  isCurrentMonth={day.isCurrentMonth}
                />
              </div>
            ))}
          </div>
        </div>
      );
    });
  };

  const monthName = currentMonth.toLocaleString('da-DK', { month: 'long' });
  const year = currentMonth.getFullYear();

  return (
    <div className="w-full max-w-6xl mx-auto flex flex-col items-center">
      {/* Weekend toggle button */}
      <div className="absolute top-4 right-4 z-10">
        <button
          onClick={() => setShowWeekends(!showWeekends)}
          className={`
            px-3 py-1.5 rounded-md shadow-sm text-xs font-medium
            transition-colors duration-200 ease-in-out
            ${darkMode
              ? 'bg-gray-800 text-blue-400 hover:bg-gray-700 border border-gray-700'
              : 'bg-blue-100 text-blue-700 hover:bg-blue-200 border border-blue-300'}
          `}
        >
          {showWeekends ? 'Skjul weekender' : 'Vis weekender'}
        </button>
      </div>

      <div className="w-full text-center mb-8">
        <h2 className={`text-4xl font-bold transition-colors duration-200 mb-5 ${darkMode ? 'text-white' : 'text-gray-800'}`}>
          <span className="inline-block">{monthName}</span>
          <span className="inline-block ml-2">{year}</span>
        </h2>

        <div className="flex justify-center space-x-4">
          <button
            onClick={handlePrevMonth}
            className={`px-5 py-2.5 rounded-full shadow-md text-sm font-medium transition-all duration-200 transform hover:scale-105 ${darkMode ? 'bg-indigo-700 text-white hover:bg-indigo-600' : 'bg-indigo-500 text-white hover:bg-indigo-400'}`}
          >
            <div className="flex items-center justify-between w-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span className="mx-auto">Forrige</span>
              <div className="w-5"></div> {/* Spacer to balance the button */}
            </div>
          </button>
          <button
            onClick={handleNextMonth}
            className={`px-5 py-2.5 rounded-full shadow-md text-sm font-medium transition-all duration-200 transform hover:scale-105 ${darkMode ? 'bg-indigo-700 text-white hover:bg-indigo-600' : 'bg-indigo-500 text-white hover:bg-indigo-400'}`}
          >
            <div className="flex items-center justify-between w-full">
              <div className="w-5"></div> {/* Spacer to balance the button */}
              <span className="mx-auto">Næste</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          </button>
        </div>
      </div>

      <div className="w-full">
        {viewType === 'calendar' && (
          <div className={`border rounded-xl shadow-lg transition-colors duration-200 ${darkMode ? 'border-gray-700 shadow-gray-900/20 bg-gray-800' : 'border-gray-300 shadow-gray-200 bg-white'}`}>
            <div className="relative p-4 pl-14 space-y-4"> {/* Added left padding (pl-14) to make room for week numbers */}
              {/* Save as PDF button */}

              {renderCalendar()}
            </div>
          </div>
        )}

        {viewType === 'list' && (
          <div className="text-center p-8">
            <p className={`text-lg ${darkMode ? 'text-white' : 'text-gray-800'}`}>Liste visning kommer snart!</p>
          </div>
        )}

        {viewType === 'timeline' && (
          <div className="text-center p-8">
            <p className={`text-lg ${darkMode ? 'text-white' : 'text-gray-800'}`}>Tidslinje visning kommer snart!</p>
          </div>
        )}

        {/* Bottom navigation buttons - only show for calendar view */}
        {viewType === 'calendar' && (
          <div className="flex justify-center mt-6 space-x-4">
            <button
              onClick={handlePrevMonth}
              className={`px-5 py-2.5 rounded-full shadow-md text-sm font-medium transition-all duration-200 transform hover:scale-105 ${darkMode ? 'bg-indigo-700 text-white hover:bg-indigo-600' : 'bg-indigo-500 text-white hover:bg-indigo-400'}`}
            >
              <div className="flex items-center justify-between w-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span className="mx-auto">Forrige</span>
                <div className="w-5"></div> {/* Spacer to balance the button */}
              </div>
            </button>
            <button
              onClick={handleNextMonth}
              className={`px-5 py-2.5 rounded-full shadow-md text-sm font-medium transition-all duration-200 transform hover:scale-105 ${darkMode ? 'bg-indigo-700 text-white hover:bg-indigo-600' : 'bg-indigo-500 text-white hover:bg-indigo-400'}`}
            >
              <div className="flex items-center justify-between w-full">
                <div className="w-5"></div> {/* Spacer to balance the button */}
                <span className="mx-auto">Næste</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            </button>
          </div>
        )}

        {/* PDF button with options */}
        <div className="flex flex-col items-center mt-6 space-y-4">
          <div className="relative inline-block">
            <button
              onClick={(e) => {
                e.stopPropagation();
                const dropdown = document.getElementById('print-dropdown');
                dropdown?.classList.toggle('hidden');

                // Add click outside listener
                if (dropdown && !dropdown.classList.contains('hidden')) {
                  const dropdownElement = dropdown; // Create a non-null reference
                  const closeDropdown = (event: MouseEvent) => {
                    if (!dropdownElement.contains(event.target as Node)) {
                      dropdownElement.classList.add('hidden');
                      document.removeEventListener('click', closeDropdown);
                    }
                  };

                  // Use setTimeout to avoid immediate closing
                  setTimeout(() => {
                    document.addEventListener('click', closeDropdown);
                  }, 0);
                }
              }}
              className={`px-5 py-2.5 rounded-lg shadow-md text-sm font-medium transition-all duration-200 ${darkMode ? 'bg-blue-700 text-white hover:bg-blue-600' : 'bg-blue-600 text-white hover:bg-blue-500'}`}
            >
              <div className="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clipRule="evenodd" />
                </svg>
                <span>Print</span>
              </div>
            </button>

            <div id="print-dropdown" className="hidden absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-56 rounded-md shadow-lg z-10 overflow-visible transition-all duration-200 ease-in-out">
              {/* Arrow pointing down */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 overflow-hidden inline-block">
                <div className={`h-2 w-2 -mt-1 rotate-45 transform ${darkMode ? 'bg-gray-800' : 'bg-white'} border-r border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}></div>
              </div>
              <div className={`${darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'}`}>
                <div className={`py-2 px-3 font-medium text-sm ${darkMode ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800'}`}>
                  Vælg udskriftsformat
                </div>
                <div className="py-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // Close dropdown first, then print to avoid UI issues
                      document.getElementById('print-dropdown')?.classList.add('hidden');
                      // Small delay to ensure dropdown is closed before opening new window
                      setTimeout(() => {
                        printCalendarView();
                        // Try to focus on the print window if it exists
                        const printWindow = window.open('', 'printWindow');
                        if (printWindow) printWindow.focus();
                      }, 50);
                    }}
                    className={`flex items-center w-full text-left px-4 py-3 text-sm ${darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'}`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 mr-3 ${darkMode ? 'text-blue-400' : 'text-blue-500'}`} viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v10H5V5z" clipRule="evenodd" />
                    </svg>
                    <div>
                      <div className="font-medium">Kalender</div>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Månedsoversigt med alle opgaver</div>
                    </div>
                  </button>


                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // Close dropdown first, then print to avoid UI issues
                      document.getElementById('print-dropdown')?.classList.add('hidden');
                      // Small delay to ensure dropdown is closed before opening new window
                      setTimeout(() => {
                        printTimelineView();
                        // Try to focus on the print window if it exists
                        const printWindow = window.open('', 'printWindow');
                        if (printWindow) printWindow.focus();
                      }, 50);
                    }}
                    className={`flex items-center w-full text-left px-4 py-3 text-sm ${darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'}`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 mr-3 ${darkMode ? 'text-green-400' : 'text-green-500'}`} viewBox="0 0 20 20" fill="currentColor">
                      <path d="M3 3a1 1 0 000 2h11a1 1 0 100-2H3zM3 7a1 1 0 000 2h5a1 1 0 000-2H3zM3 11a1 1 0 100 2h4a1 1 0 100-2H3zM13 16a1 1 0 102 0v-5.586l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 101.414 1.414L13 10.414V16z" />
                    </svg>
                    <div>
                      <div className="font-medium">Tidslinje</div>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Kronologisk oversigt over alle opgaver</div>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Authentication button */}
          {user ? (
            <button
              onClick={() => {
                signout();
                onHideAuth?.();
              }}
              className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${
                darkMode
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : 'bg-red-500 hover:bg-red-600 text-white'
              }`}
            >
              Log ud
            </button>
          ) : (
            <button
              onClick={onShowAuth}
              className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${
                darkMode
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              Log ind
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Calendar;
