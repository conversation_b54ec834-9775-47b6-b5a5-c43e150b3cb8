import React from 'react';

interface ColorPickerProps {
  selectedColor: string;
  onColorSelect: (color: string) => void;
  darkMode?: boolean;
}

const ColorPicker: React.FC<ColorPickerProps> = ({ selectedColor, onColorSelect, darkMode = false }) => {
  // Predefined color palette
  const colors = [
    '#4f46e5', // Indigo
    '#2563eb', // Blue
    '#0891b2', // Cyan
    '#059669', // Emerald
    '#16a34a', // Green
    '#84cc16', // Lime
    '#eab308', // Yellow
    '#f59e0b', // Amber
    '#d97706', // Orange
    '#dc2626', // Red
    '#db2777', // Pink
    '#9333ea', // Purple
    '#6b7280', // Gray
  ];

  return (
    <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700/50' : 'bg-gray-100'}`}>
      <div className="flex flex-wrap gap-2.5 justify-center">
        {colors.map((color) => (
          <button
            key={color}
            type="button"
            className={`w-9 h-9 rounded-full border-2 transition-all duration-200 transform hover:scale-110 ${
              selectedColor === color
                ? (darkMode ? 'border-white shadow-lg' : 'border-gray-800 shadow-md')
                : 'border-transparent'
            }`}
            style={{ backgroundColor: color }}
            onClick={() => onColorSelect(color)}
            aria-label={`Vælg farve ${color}`}
          />
        ))}

        <div className={`w-9 h-9 rounded-full overflow-hidden relative ${darkMode ? 'bg-gray-600' : 'bg-white border border-gray-300'}`}>
          <input
            type="color"
            value={selectedColor}
            onChange={(e) => onColorSelect(e.target.value)}
            className="absolute inset-0 w-full h-full cursor-pointer opacity-0"
            aria-label="Brugerdefineret farvevælger"
          />
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
            </svg>
          </div>
        </div>
      </div>

      <div className="mt-3 flex items-center justify-center">
        <div
          className="w-full h-6 rounded-md border shadow-inner overflow-hidden"
          style={{
            backgroundColor: selectedColor,
            borderColor: darkMode ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.1)'
          }}
        />
      </div>
    </div>
  );
};

export default ColorPicker;
