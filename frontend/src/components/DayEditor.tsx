import { useState, useEffect } from 'react';
import { DayData } from '../types';
import ColorPicker from './ColorPicker';

interface DayEditorProps {
  date: Date;
  dayData?: DayData | null;
  onSave: (data: DayData) => void;
  onCancel: () => void;
  onDelete?: () => void;
  darkMode?: boolean;
}

const DayEditor: React.FC<DayEditorProps> = ({ date, dayData, onSave, onCancel, onDelete, darkMode = false }) => {
  const [name, setName] = useState(dayData?.name || '');
  const [color, setColor] = useState(dayData?.color || '#4f46e5');
  const [description, setDescription] = useState(dayData?.description || '');
  // Initialize end date
  const [endDate, setEndDate] = useState(new Date());

  // Set the end date when the component mounts or when date/dayData changes
  useEffect(() => {
    if (dayData?.endDate) {
      // For existing tasks, use the stored end date
      setEndDate(new Date(dayData.endDate));
    } else {
      // For new tasks, set the end date to the same as the task date
      const newEndDate = new Date(date.getTime());
      setEndDate(newEndDate);
    }
  }, [date, dayData]);

  // Add click outside handler to close the editor
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Get the editor container element
      const editorContainer = document.querySelector('.day-editor-container');

      // If the click is outside the editor container, close the editor
      if (editorContainer && !editorContainer.contains(event.target as Node)) {
        onCancel();
      }
    };

    // Add the event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Clean up the event listener when the component unmounts
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onCancel]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const newDayData: DayData = {
      id: dayData?.id || crypto.randomUUID(),
      date: new Date(date),
      name,
      color,
      description: description || undefined
    };

    // Always include endDate in the day data
    newDayData.endDate = endDate;

    onSave(newDayData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 overflow-y-auto p-4 sm:p-6 md:p-10">
      <div className="min-h-[calc(100vh-8rem)] flex items-center justify-center w-full">
        <div className={`day-editor-container rounded-xl p-4 sm:p-6 w-full max-w-md transition-colors duration-200 shadow-2xl my-auto ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <div className="flex justify-between items-center mb-4">
            <h2 className={`text-xl font-bold transition-colors duration-200 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {date.toLocaleDateString('da-DK', { weekday: 'long', month: 'long', day: 'numeric' })}
            </h2>
            <button
              type="button"
              onClick={onCancel}
              className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 ${darkMode ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'}`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-5">
            <div>
              <label htmlFor="name" className={`block text-sm font-medium mb-1.5 transition-colors duration-200 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Navn
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className={`w-full px-4 py-2.5 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors duration-200 ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'}`}
                placeholder="Opgavenavn"
                required
              />
            </div>

            <div>
              <label className={`block text-sm font-medium mb-1.5 transition-colors duration-200 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Farve
              </label>
              <ColorPicker selectedColor={color} onColorSelect={setColor} darkMode={darkMode} />
            </div>

            <div>
              <label htmlFor="description" className={`block text-sm font-medium mb-1.5 transition-colors duration-200 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Beskrivelse (valgfri)
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className={`w-full px-4 py-2.5 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors duration-200 ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'}`}
                placeholder="Tilføj en beskrivelse..."
              />
            </div>

            <div>
              <label className={`block text-sm font-medium mb-1.5 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Slutdato
              </label>
              <input
                id="endDate"
                type="date"
                value={(() => {
                  // Format the date as YYYY-MM-DD for the input
                  const year = endDate.getFullYear();
                  const month = String(endDate.getMonth() + 1).padStart(2, '0');
                  const day = String(endDate.getDate()).padStart(2, '0');
                  return `${year}-${month}-${day}`;
                })()}
                onChange={(e) => {
                  const newDate = new Date(e.target.value);
                  if (!isNaN(newDate.getTime())) {
                    setEndDate(newDate);

                    // Close the datepicker by blurring the input after a short delay
                    setTimeout(() => {
                      e.target.blur();
                    }, 100);
                  }
                }}
                className={`w-full px-4 py-2.5 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors duration-200 ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'}`}
              />
            </div>

            <div className="flex space-x-3">
              <button
                type="submit"
                className={`flex-1 py-2.5 border border-transparent rounded-lg text-sm font-medium text-white transition-colors duration-200 shadow-md ${darkMode ? 'bg-indigo-700 hover:bg-indigo-800' : 'bg-indigo-600 hover:bg-indigo-700'}`}
              >
                Gem
              </button>
              {dayData && onDelete && (
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    onDelete();
                    onCancel(); // Close the window after deletion
                  }}
                  className={`py-2.5 px-4 border border-transparent rounded-lg text-sm font-medium transition-colors duration-200 shadow-md ${darkMode ? 'bg-red-700 hover:bg-red-800 text-white' : 'bg-red-600 hover:bg-red-700 text-white'}`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default DayEditor;
